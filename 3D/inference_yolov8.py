#!/usr/bin/env python3
"""
YOLOv8 推理脚本
"""

from ultralytics import YOLO
import cv2
import os
from pathlib import Path

def main():
    # 加载训练好的模型
    model_path = 'runs/detect/custom_yolov8/weights/best.pt'
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("请先完成训练")
        return
    
    model = YOLO(model_path)
    
    # 测试图片目录
    test_dir = 'yolo_dataset/test/images'
    output_dir = 'inference_results'
    
    if not os.path.exists(test_dir):
        test_dir = 'yolo_dataset/val/images'  # 如果没有测试集，使用验证集
    
    if not os.path.exists(test_dir):
        print(f"❌ 测试目录不存在: {test_dir}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 批量推理
    results = model(test_dir, save=True, project=output_dir, name='predictions')
    
    print(f"✅ 推理完成! 结果保存在: {output_dir}/predictions")
    
    # 显示统计信息
    total_images = len(list(Path(test_dir).glob('*.jpg'))) + len(list(Path(test_dir).glob('*.png')))
    print(f"📊 处理了 {total_images} 张图片")

if __name__ == '__main__':
    main()
